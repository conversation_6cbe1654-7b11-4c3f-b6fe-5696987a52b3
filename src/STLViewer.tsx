import { useEffect, useRef } from 'preact/hooks'
import type { FunctionComponent } from "preact"
import * as THREE from 'three'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

interface STLViewerProps {
    fileBuffer: ArrayBuffer
}

export const STLViewer: FunctionComponent<STLViewerProps> = ({ fileBuffer }) => {
    const mountRef = useRef<HTMLDivElement>(null)
    const sceneRef = useRef<{
        scene: THREE.Scene
        camera: THREE.PerspectiveCamera
        renderer: THREE.WebGLRenderer
        controls: OrbitControls
        mesh?: THREE.Mesh
        animationId?: number
    }>()

    useEffect(() => {
        if (!mountRef.current || !fileBuffer) return

        // Scene setup
        const scene = new THREE.Scene()
        scene.background = new THREE.Color(0xf5f5f5)

        // Camera setup
        const camera = new THREE.PerspectiveCamera(
            75,
            mountRef.current.clientWidth / mountRef.current.clientHeight,
            0.1,
            1000
        )

        // Renderer setup
        const renderer = new THREE.WebGLRenderer({ antialias: true })
        renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight)
        renderer.shadowMap.enabled = true
        renderer.shadowMap.type = THREE.PCFSoftShadowMap
        mountRef.current.appendChild(renderer.domElement)

        // Controls setup
        const controls = new OrbitControls(camera, renderer.domElement)
        controls.enableDamping = true
        controls.dampingFactor = 0.05
        controls.enableZoom = true
        controls.enablePan = true

        // Lighting setup
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
        scene.add(ambientLight)

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
        directionalLight.position.set(10, 10, 5)
        directionalLight.castShadow = true
        directionalLight.shadow.mapSize.width = 2048
        directionalLight.shadow.mapSize.height = 2048
        scene.add(directionalLight)

        const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.4)
        directionalLight2.position.set(-10, -10, -5)
        scene.add(directionalLight2)

        // Store scene references
        sceneRef.current = { scene, camera, renderer, controls }

        // Load STL
        const loader = new STLLoader()
        const geometry = loader.parse(fileBuffer)

        // Center the geometry
        geometry.computeBoundingBox()
        const boundingBox = geometry.boundingBox!
        const center = new THREE.Vector3()
        boundingBox.getCenter(center)
        geometry.translate(-center.x, -center.y, -center.z)


        // Create material with static light blue color
        const meshMaterial = new THREE.MeshPhongMaterial({
            color: 0x87CEEB, // Light blue color
            shininess: 30,
            transparent: true,
            opacity: 0.9
        })

        // Create mesh
        const mesh = new THREE.Mesh(geometry, meshMaterial)
        mesh.castShadow = true
        mesh.receiveShadow = true
        scene.add(mesh)
        sceneRef.current.mesh = mesh

        // Position camera to show the entire model
        const size = new THREE.Vector3()
        boundingBox.getSize(size)
        const maxDim = Math.max(size.x, size.y, size.z)
        const distance = maxDim * 2
        camera.position.set(distance, distance, distance)
        camera.lookAt(0, 0, 0)
        controls.update()

        // Animation loop
        const animate = () => {
            sceneRef.current!.animationId = requestAnimationFrame(animate)
            controls.update()
            renderer.render(scene, camera)
        }
        animate()

        // Handle resize
        const handleResize = () => {
            if (!mountRef.current) return
            const width = mountRef.current.clientWidth
            const height = mountRef.current.clientHeight
            camera.aspect = width / height
            camera.updateProjectionMatrix()
            renderer.setSize(width, height)
        }
        window.addEventListener('resize', handleResize)

        // Cleanup function
        return () => {
            window.removeEventListener('resize', handleResize)
            if (sceneRef.current?.animationId) {
                cancelAnimationFrame(sceneRef.current.animationId)
            }
            if (mountRef.current && renderer.domElement) {
                mountRef.current.removeChild(renderer.domElement)
            }
            geometry.dispose()
            meshMaterial.dispose()
            renderer.dispose()
        }
    }, [fileBuffer])

    return (
        <div className="stl-viewer">
            <div className="viewer-header">
                <h4>3D Preview</h4>
                <div className="viewer-controls">
                    <span className="control-hint">🖱️ Drag to rotate • 🔍 Scroll to zoom • ⌨️ Right-click to pan</span>
                </div>
            </div>
            <div ref={mountRef} className="viewer-container" />
        </div>
    )
}
