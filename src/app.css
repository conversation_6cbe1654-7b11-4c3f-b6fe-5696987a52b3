body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    box-sizing: border-box;
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 50%, #e1bee7 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

.app {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px;
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(255, 105, 180, 0.3);
    border: 3px solid #ff69b4;
}

.cute-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    border-radius: 15px;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
}

.cute-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: bounce 2s infinite;
}

.cute-header p {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

.app-layout {
    display: flex;
    gap: 32px;
    align-items: flex-start;
}

.main-content {
    flex: 1;
    min-width: 0;
    /* Prevents flex item from overflowing */
}

.cart-sidebar {
    flex: 0 0 400px;
    /* Fixed width for cart */
    position: sticky;
    top: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* STL Viewer Styles */
.stl-viewer {
    margin-top: 24px;
    border: 3px solid #ff69b4;
    border-radius: 20px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
}

.viewer-header {
    padding: 16px 20px;
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    border-bottom: 1px solid #ff1493;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    position: relative;
}

.viewer-header::before {
    content: "🐾";
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2em;
    opacity: 0.7;
}

.viewer-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    margin-left: 30px;
}

.viewer-controls {
    display: flex;
    align-items: center;
}

.control-hint {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.viewer-container {
    width: 100%;
    height: 350px;
    position: relative;
    background-color: #f5f5f5;
}

/* When STL viewer is in sidebar, make it more compact */
.cart-sidebar .viewer-container {
    height: 300px;
}

.viewer-container canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}

/* Mobile responsive - stack vertically on smaller screens */
@media (max-width: 768px) {
    .app-layout {
        flex-direction: column;
        gap: 24px;
    }

    .cart-sidebar {
        flex: none;
        position: static;
        width: 100%;
    }

    .app {
        max-width: 600px;
    }

    .viewer-container {
        height: 300px;
    }

    .control-hint {
        display: none;
    }
}


#result {
    margin-top: 32px;
    opacity: 0;
    transition: opacity 0.25s ease;
}

#result.visible {
    opacity: 1;
}

.result-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
    padding: 24px;
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    border: 3px solid #ff69b4;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
    position: relative;
    overflow: hidden;
}

.result-content::before {
    content: "💖";
    position: absolute;
    top: 10px;
    left: 15px;
    font-size: 1.5em;
    opacity: 0.3;
}

.result-content::after {
    content: "🐾";
    position: absolute;
    bottom: 10px;
    right: 15px;
    font-size: 1.5em;
    opacity: 0.3;
}

.price-section {
    flex: 1;
}

.price-label {
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-value {
    font-size: 36px;
    font-weight: 700;
    color: #ff1493;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(255, 20, 147, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.dropzone {
    border: 3px dashed #ff69b4;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-weight: bold;
}

.dropzone::before {
    content: "🐾";
    position: absolute;
    top: 10px;
    left: 15px;
    font-size: 1.5em;
    opacity: 0.3;
}

.dropzone::after {
    content: "🐾";
    position: absolute;
    bottom: 10px;
    right: 15px;
    font-size: 1.5em;
    opacity: 0.3;
}

.dropzone:hover {
    border-color: #ff1493;
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    color: white;
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.dropzone.drag-active {
    border-color: #ff1493;
    background: linear-gradient(135deg, #ff1493 0%, #c71585 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 20, 147, 0.6);
}

.dropzone-subtext {
    font-size: 0.9em;
    opacity: 0.8;
    font-style: italic;
}

/* Material Selection Styles */
.material-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
}

.material-option {
    position: relative;
}

.material-radio {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.material-label {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    border: 3px solid #ff69b4;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.material-label::before {
    content: "🎀";
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2em;
    opacity: 0.6;
}

.material-label:hover {
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    border-color: #ff1493;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.material-radio:checked+.material-label {
    background: linear-gradient(135deg, #ff1493 0%, #c71585 100%);
    border-color: #c71585;
    color: white;
    box-shadow: 0 8px 25px rgba(199, 21, 133, 0.5);
    transform: scale(1.02);
}

.material-radio:checked+.material-label::before {
    content: "💖";
}

.material-radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #ff69b4;
    border-radius: 50%;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    background-color: white;
}

.material-radio:checked+.material-label .material-radio-custom {
    border-color: #ff1493;
    background-color: #ff1493;
    box-shadow: 0 0 10px rgba(255, 20, 147, 0.5);
}

.material-radio:checked+.material-label .material-radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
}

.material-name {
    font-weight: 600;
    color: #333;
    flex: 1;
}

.material-cost {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.add-to-cart-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 12px;
    flex-shrink: 0;
}

.quantity-input {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.quantity-input label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quantity-field {
    width: 50px;
    padding: 4px 6px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    font-weight: 600;
}

.quantity-field:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.add-to-cart-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.add-to-cart-btn:hover::before {
    left: 100%;
}

.add-to-cart-btn:hover {
    background: linear-gradient(135deg, #ff1493 0%, #c71585 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 20, 147, 0.6);
}

.add-to-cart-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .result-content {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }

    .add-to-cart-section {
        align-items: stretch;
    }

    .quantity-input {
        justify-content: center;
    }
}

.cart {
    margin-top: 32px;
    padding: 25px;
    border: 3px solid #ff69b4;
    border-radius: 20px;
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.3);
    position: relative;
}

.cart::before {
    content: "🛒";
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.5em;
    opacity: 0.6;
}

.cart h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #ff1493;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(255, 20, 147, 0.3);
    font-size: 1.5em;
}

.cart-empty {
    color: #666;
    font-style: italic;
}

.cart-entries {
    margin-bottom: 16px;
}

.cart-entry {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 15px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #fce4ec 100%);
    border: 2px solid #ff69b4;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.2);
    position: relative;
}

.cart-entry::before {
    content: "🎀";
    position: absolute;
    top: 8px;
    right: 10px;
    font-size: 1em;
    opacity: 0.4;
}

.cart-entry-info {
    flex: 1;
}

.cart-entry-filename {
    font-weight: bold;
    margin-bottom: 4px;
}

.cart-entry-material {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.cart-entry-price {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.cart-entry-total {
    font-weight: bold;
    color: #007bff;
}

.cart-entry-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cart-entry-quantity {
    display: flex;
    align-items: center;
    gap: 6px;
}

.cart-entry-quantity label {
    font-size: 12px;
    font-weight: 500;
    color: #666;
}

.cart-quantity-input {
    width: 50px;
    padding: 2px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
    text-align: center;
}

.cart-quantity-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
}

.cart-entry-remove {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: background-color 0.2s ease;
}

.cart-entry-remove:hover {
    background-color: #c82333;
}

.cart-total {
    padding: 15px;
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    border-radius: 15px;
    text-align: center;
    margin-bottom: 15px;
    color: white;
    font-weight: 700;
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
}

.cart-clear {
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
}

.cart-clear:hover {
    background: linear-gradient(135deg, #ff1493 0%, #c71585 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 20, 147, 0.5);
}