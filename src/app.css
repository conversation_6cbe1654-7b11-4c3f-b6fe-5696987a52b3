body {
    font-family: Arial, Helvetica, sans-serif;
    box-sizing: border-box;
}


#result {
    margin-top: 24px;
    border-top: 1px solid black;
    padding-top: 12px;
    opacity: 0;
    transition: opacity;
    transition-duration: 0.25s;
}

#result.visible {
    opacity: 1
}

#result p {
    display: inline;
}

#price {
    font-weight: bold;
    border-bottom: 2px black solid;
    font-size: 2rem;
}

.dropzone {
    border: 2px dashed #ccc;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    background-color: white;
    transition: background-color 0.2s ease;
}

.dropzone.drag-active {
    background-color: #f0f0f0;
}