body {
    font-family: Arial, Helvetica, sans-serif;
    box-sizing: border-box;
}


#result {
    margin-top: 24px;
    border-top: 1px solid black;
    padding-top: 12px;
    opacity: 0;
    transition: opacity;
    transition-duration: 0.25s;
}

#result.visible {
    opacity: 1
}

#result p {
    display: inline;
}

#price {
    font-weight: bold;
    border-bottom: 2px black solid;
    font-size: 2rem;
}

.dropzone {
    border: 2px dashed #ccc;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    background-color: white;
    transition: background-color 0.2s ease;
}

.dropzone.drag-active {
    background-color: #f0f0f0;
}

.add-to-cart-section {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.quantity-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quantity-input label {
    font-size: 14px;
    font-weight: 500;
}

.quantity-field {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.quantity-field:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.add-to-cart-btn {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.add-to-cart-btn:hover {
    background-color: #0056b3;
}

.cart {
    margin-top: 32px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.cart h2 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #333;
}

.cart-empty {
    color: #666;
    font-style: italic;
}

.cart-entries {
    margin-bottom: 16px;
}

.cart-entry {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px;
    margin-bottom: 8px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.cart-entry-info {
    flex: 1;
}

.cart-entry-filename {
    font-weight: bold;
    margin-bottom: 4px;
}

.cart-entry-material {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.cart-entry-price {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.cart-entry-total {
    font-weight: bold;
    color: #007bff;
}

.cart-entry-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cart-entry-quantity {
    display: flex;
    align-items: center;
    gap: 6px;
}

.cart-entry-quantity label {
    font-size: 12px;
    font-weight: 500;
    color: #666;
}

.cart-quantity-input {
    width: 50px;
    padding: 2px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
    text-align: center;
}

.cart-quantity-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
}

.cart-entry-remove {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: background-color 0.2s ease;
}

.cart-entry-remove:hover {
    background-color: #c82333;
}

.cart-total {
    padding: 12px;
    background-color: #e9ecef;
    border-radius: 4px;
    text-align: left;
    margin-bottom: 12px;
}

.cart-clear {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.cart-clear:hover {
    background-color: #5a6268;
}