type Vector = { x: number, y: number, z: number };

// Function for calculating volume
function signedVolumeOfTriangle(p1: Vector, p2: Vector, p3: Vector) {
    var v321 = p3.x * p2.y * p1.z;
    var v231 = p2.x * p3.y * p1.z;
    var v312 = p3.x * p1.y * p2.z;
    var v132 = p1.x * p3.y * p2.z;
    var v213 = p2.x * p1.y * p3.z;
    var v123 = p1.x * p2.y * p3.z;
    return (-v321 + v231 + v312 - v132 - v213 + v123) / 6;
}

// STL parser function
function parseSTL(buffer: ArrayBuffer) {
    const view = new DataView(buffer);

    // Check if binary STL
    const isBinary = detectBinarySTL(buffer, view);

    if (isBinary) {
        return parseBinarySTL(buffer, view);
    } else {
        return parseAsciiSTL(buffer);
    }
}

function detectBinarySTL(buffer: ArrayBuffer, view: DataView) {
    // Skip header
    const header = new Uint8Array(buffer, 0, 80);

    // Get number of triangles
    const triangles = view.getUint32(80, true);

    // Binary STL size check
    return buffer.byteLength === triangles * 50 + 84;
}

function parseBinarySTL(buffer: ArrayBuffer, view: DataView) {
    const triangles = view.getUint32(80, true);
    const positions = [];

    for (let i = 0; i < triangles; i++) {
        const offset = 84 + i * 50;

        // Skip normal

        // Get vertices
        for (let j = 0; j < 3; j++) {
            const vertexOffset = offset + 12 + j * 12;

            const x = view.getFloat32(vertexOffset, true);
            const y = view.getFloat32(vertexOffset + 4, true);
            const z = view.getFloat32(vertexOffset + 8, true);

            positions.push([x, y, z]);
        }
    }

    return { positions };
}

function parseAsciiSTL(buffer: ArrayBuffer) {
    // Basic ASCII STL parser
    const text = new TextDecoder().decode(buffer);
    const lines = text.split('\n');
    const positions = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.startsWith('vertex')) {
            const parts = line.split(/\s+/);
            if (parts.length >= 4) {
                positions.push([
                    parseFloat(parts[1]),
                    parseFloat(parts[2]),
                    parseFloat(parts[3])
                ]);
            }
        }
    }

    return { positions };
}

export function calculateVolumeAndDimensions(file: ArrayBuffer) {
    const mesh = parseSTL(file);

    const positions = mesh.positions;
    const objectPolygons = positions.length / 3;
    let objectVolume = 0;
    const dimensions = [
        { bottom: 0, top: 0, diff: 0 },
        { bottom: 0, top: 0, diff: 0 },
        { bottom: 0, top: 0, diff: 0 }
    ];

    for (let i = 0; i < positions.length; i += 3) {
        const t1 = {
            x: positions[i][0],
            y: positions[i][1],
            z: positions[i][2]
        };

        const t2 = {
            x: positions[i + 1][0],
            y: positions[i + 1][1],
            z: positions[i + 1][2]
        };

        const t3 = {
            x: positions[i + 2][0],
            y: positions[i + 2][1],
            z: positions[i + 2][2]
        };

        // Calculate volume
        objectVolume += signedVolumeOfTriangle(t1, t2, t3);

        // Calculate dimensions
        for (let j = 0; j < 3; j++) {
            for (let k = 0; k < 3; k++) {
                if (dimensions[j].top < positions[i + k][j]) {
                    dimensions[j].top = positions[i + k][j];
                }
                if (dimensions[j].bottom > positions[i + k][j]) {
                    dimensions[j].bottom = positions[i + k][j];
                }
            }
        }
    }

    // Convert volume to cm³
    objectVolume = Math.abs(objectVolume) * 0.001;

    // Calculate differences
    for (let i = 0; i < 3; i++) {
        dimensions[i].diff = dimensions[i].top - dimensions[i].bottom;
    }

    return {
        objectVolume,
        objectPolygons,
        dimensions
    };
}