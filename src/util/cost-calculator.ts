import type { MATERIALS } from "../settings";

export function calculateCost(volume: number, material: typeof MATERIALS[number]) {
    const materialCost = volume * material.cost;
    return materialCost;
}

/*

    // Constants
    PRINTER_DIMENSIONS: [255, 255, 255], // mm
    CURRENCY_SYMBOL: "€",
    CHARGE_ADD_BASE: 3.5,
    CHARGE_ADD_PERCENT: 0.30,

    // Material costs in €/mm³
    MATERIAL_COSTS: {
        pla: 0.0213,
        petg: 0.0313,
        nylon: 0.0473,
        abs: 0.0212,
        pctg: 0.0353,
    },



    -------------------------


calculateCost(result, materialChoice) {
        // Check if object fits in printer
        let clientFeedback = '';
        for (let i = 0; i < 3; i++) {
            if (result.dimensions[i].diff > this.PRINTER_DIMENSIONS[i]) {
                clientFeedback += "<br>Object larger than print bed on dimension " + i + " (" +
                    (result.dimensions[i].diff) + "mm > " + this.PRINTER_DIMENSIONS[i] + "mm)";
            }
        }

        // Get unit charge
        const unitCharge = this.MATERIAL_COSTS[materialChoice];

        // Calculate total cost
        let chargeTotal = result.objectVolume * unitCharge;
        chargeTotal += chargeTotal * this.CHARGE_ADD_PERCENT;
        chargeTotal += this.CHARGE_ADD_BASE;
        chargeTotal = chargeTotal.toFixed(2);

        return {
            unitCharge,
            chargeAddBase: this.CHARGE_ADD_BASE,
            chargeAddPercent: this.CHARGE_ADD_PERCENT,
            chargeTotal,
            currencySymbol: this.CURRENCY_SYMBOL,
            clientFeedback
        };
    },

    getCurrencySymbol() {
        return this.CURRENCY_SYMBOL;
    }
*/