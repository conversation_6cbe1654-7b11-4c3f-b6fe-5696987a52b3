import { useEffect, useState } from 'preact/hooks'
import './app.css'
import { MATERIALS } from './settings'
import { calculateVolumeAndDimensions } from './util/volume-calculator'
import { calculateCost } from './util/cost-calculator'
import { Cart } from './cart'
import { FileUploader } from 'react-drag-drop-files'
import Dropzone from 'react-dropzone'


type Entry = { file: ArrayBuffer, stats: ReturnType<typeof calculateVolumeAndDimensions>, cost: number }

export function App() {

  const [activeMaterial, setActiveMaterial] = useState(MATERIALS[0])
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  const [calculation, setCalculation] = useState<Entry>();

  useEffect(() => {
    if (uploadedFile) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const buffer = e.target?.result as Array<PERSON>uffer
        const modelStats = calculateVolumeAndDimensions(buffer)
        const cost = calculateCost(modelStats.objectVolume, activeMaterial)
        setCalculation({ file: buffer, stats: modelStats, cost })
      }
      reader.readAsArrayBuffer(uploadedFile)
    }
  }, [uploadedFile, activeMaterial])

  console.log("bla", calculation)
  return (
    <div class="app">
      <h1>Print Calculator</h1>

      <form id='uploadForm'>

        <h3>Material</h3>
        {MATERIALS.map((material) => {
          const materialName = material.name
          return (
            <>
              <input checked={activeMaterial === material} type="radio" id={`materialPick-${materialName}`} name="material" value={materialName} onClick={() => setActiveMaterial(material)} />
              <label for={`materialPick-${materialName}`}>{materialName.toUpperCase()}</label>
            </>
          )
        }
        )}

        <h3>STL file</h3>
        <Dropzone onDrop={(e) => console.log(e)}>
          {/* <div>Try dropping some files here, or click to select files to upload.</div> */}
        </Dropzone>

      </form>

      <div id="result" class={calculation ? 'visible' : ''}><p>Price: </p><p id="price">{calculation && calculation.cost.toFixed(2)}€</p></div>
      {/* <Cart /> */}
    </div>
  )
}
