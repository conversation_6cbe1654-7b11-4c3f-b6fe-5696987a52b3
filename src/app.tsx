import { useEffect, useState } from 'preact/hooks'
import './app.css'
import { MATERIALS } from './settings'
import { calculateVolumeAndDimensions } from './util/volume-calculator'
import { calculateCost } from './util/cost-calculator'
import { Cart, CartComponent } from './cart'
import { STLViewer } from './STLViewer'
import Dropzone from 'react-dropzone'


type Entry = { file: ArrayBuffer, stats: ReturnType<typeof calculateVolumeAndDimensions>, cost: number }

export function App() {

  const [activeMaterial, setActiveMaterial] = useState(MATERIALS[0])
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  const [calculation, setCalculation] = useState<Entry>();
  const [cart] = useState(() => new Cart())
  const [quantity, setQuantity] = useState(1)

  useEffect(() => {
    if (uploadedFile) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const buffer = e.target?.result as Array<PERSON>uffer
        const modelStats = calculateVolumeAndDimensions(buffer)
        const cost = calculateCost(modelStats.objectVolume, activeMaterial)
        setCalculation({ file: buffer, stats: modelStats, cost })
      }
      reader.readAsArrayBuffer(uploadedFile)
    }
  }, [uploadedFile, activeMaterial])

  const addToCart = () => {
    if (uploadedFile && calculation) {
      cart.addEntry(uploadedFile.name, activeMaterial, calculation.cost, calculation.file, quantity)
      setQuantity(1) // Reset quantity after adding to cart
    }
  }

  return (
    <div class="app">
      <h1>Print Calculator</h1>

      <div className="app-layout">
        <div className="main-content">
          <form id='uploadForm'>

            <h3>Material</h3>
            <div className="material-options">
              {MATERIALS.map((material) => {
                const materialName = material.name
                return (
                  <div key={materialName} className="material-option">
                    <input
                      checked={activeMaterial === material}
                      type="radio"
                      id={`materialPick-${materialName}`}
                      name="material"
                      value={materialName}
                      onChange={() => setActiveMaterial(material)}
                      className="material-radio"
                    />
                    <label htmlFor={`materialPick-${materialName}`} className="material-label">
                      <span className="material-radio-custom"></span>
                      <span className="material-name">{materialName.toUpperCase()}</span>
                      <span className="material-cost">€{material.cost}/g</span>
                    </label>
                  </div>
                )
              })}
            </div>

            <h3>STL file</h3>
            <Dropzone onDrop={(acceptedFiles) => {
              if (acceptedFiles.length > 0) {
                setUploadedFile(acceptedFiles[0]);
              }
            }}>
              {({ getRootProps, getInputProps, isDragActive }) => {
                const rootProps = getRootProps();
                return (
                  <div {...(rootProps as any)} className={`dropzone ${isDragActive ? 'drag-active' : ''}`}>
                    <input {...getInputProps()} />
                    {isDragActive ? (
                      <p>Drop the STL file here...</p>
                    ) : (
                      <p>Drag and drop an STL file here, or click to select one</p>
                    )}
                  </div>
                )
              }}
            </Dropzone>

          </form>

          <div id="result" class={calculation ? 'visible' : ''}>
            <div className="result-content">
              <div className="price-section">
                <div className="price-label">Estimated Cost</div>
                <div className="price-value">€{calculation && calculation.cost.toFixed(2)}</div>
              </div>

              {calculation && uploadedFile && (
                <div className="add-to-cart-section">
                  <div className="quantity-input">
                    <label htmlFor="quantity">Qty:</label>
                    <input
                      id="quantity"
                      type="number"
                      min="1"
                      value={quantity}
                      onChange={(e) => setQuantity(parseInt(e.currentTarget.value) || 1)}
                      className="quantity-field"
                    />
                  </div>
                  <button className="add-to-cart-btn" onClick={addToCart}>
                    Add to Cart
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="cart-sidebar">
          {calculation && uploadedFile && (
            <STLViewer
              fileBuffer={calculation.file}
              material={activeMaterial}
            />
          )}
          <CartComponent cart={cart} />
        </div>
      </div>
    </div>
  )
}
