import { useEffect, useRef, useState } from 'preact/hooks'
import './app.css'
import { MATERIALS } from './settings'
import { calculateVolumeAndDimensions } from './util/volume-calculator'
import { calculateCost } from './util/cost-calculator'
import { Cart } from './cart'
import Dropzone from 'react-dropzone'


type Entry = { file: ArrayBuffer, stats: ReturnType<typeof calculateVolumeAndDimensions>, cost: number }

export function App() {

  const [activeMaterial, setActiveMaterial] = useState(MATERIALS[0])
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  const [calculation, setCalculation] = useState<Entry>();
  const cartRef = useRef<Cart>(new Cart())

  useEffect(() => {
    if (uploadedFile) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const buffer = e.target?.result as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
        const modelStats = calculateVolumeAndDimensions(buffer)
        const cost = calculateCost(modelStats.objectVolume, activeMaterial)
        setCalculation({ file: buffer, stats: modelStats, cost })
      }
      reader.readAsArrayBuffer(uploadedFile)
    }
  }, [uploadedFile, activeMaterial])

  pushEntryToCart(uploadedFile, calculation) {
    if (uploadedFile && calculation) {
      setEntries([...entries, { file: uploadedFile, cost: calculation.cost }])
    }
  }

  return (
    <div class="app">
      <h1>Print Calculator</h1>

      <form id='uploadForm'>

        <h3>Material</h3>
        {MATERIALS.map((material) => {
          const materialName = material.name
          return (
            <>
              <input checked={activeMaterial === material} type="radio" id={`materialPick-${materialName}`} name="material" value={materialName} onClick={() => setActiveMaterial(material)} />
              <label for={`materialPick-${materialName}`}>{materialName.toUpperCase()}</label>
            </>
          )
        }
        )}

        <h3>STL file</h3>
        <Dropzone onDrop={(acceptedFiles) => {
          if (acceptedFiles.length > 0) {
            setUploadedFile(acceptedFiles[0]);
          }
        }}>
          {({ getRootProps, getInputProps, isDragActive }) => {
            const rootProps = getRootProps();
            return (
              <div {...(rootProps as any)} className={`dropzone ${isDragActive ? 'drag-active' : ''}`}>
                <input {...getInputProps()} />
                {isDragActive ? (
                  <p>Drop the STL file here...</p>
                ) : (
                  <p>Drag and drop an STL file here, or click to select one</p>
                )}
              </div>
            )
          }}
        </Dropzone>

      </form>

      <div id="result" class={calculation ? 'visible' : ''}><p>Price: </p><p id="price">{calculation && calculation.cost.toFixed(2)}€</p></div>
      <Cart />
    </div>
  )
}
