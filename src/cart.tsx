import { useState, useEffect } from 'preact/hooks'
import type { FunctionComponent } from "preact"
import type { MATERIALS } from './settings'

export type CartEntry = {
    id: string
    filename: string
    material: typeof MATERIALS[number]
    cost: number
    file: ArrayBuffer
}

export class Cart {
    private entries: CartEntry[] = []
    private listeners: Array<(entries: CartEntry[]) => void> = []

    addEntry(filename: string, material: typeof MATERIALS[number], cost: number, file: ArrayBuffer): void {
        const entry: CartEntry = {
            id: crypto.randomUUID(),
            filename,
            material,
            cost,
            file
        }
        this.entries.push(entry)
        this.notifyListeners()
    }

    removeEntry(id: string): void {
        this.entries = this.entries.filter(entry => entry.id !== id)
        this.notifyListeners()
    }

    getEntries(): CartEntry[] {
        return [...this.entries]
    }

    getTotalCost(): number {
        return this.entries.reduce((total, entry) => total + entry.cost, 0)
    }

    clear(): void {
        this.entries = []
        this.notifyListeners()
    }

    subscribe(listener: (entries: CartEntry[]) => void): () => void {
        this.listeners.push(listener)
        return () => {
            this.listeners = this.listeners.filter(l => l !== listener)
        }
    }

    private notifyListeners(): void {
        this.listeners.forEach(listener => listener(this.getEntries()))
    }
}

type CartComponentProps = {
    cart: Cart
}

export const CartComponent: FunctionComponent<CartComponentProps> = ({ cart }) => {
    const [entries, setEntries] = useState<CartEntry[]>(cart.getEntries())

    // Subscribe to cart changes
    useEffect(() => {
        const unsubscribe = cart.subscribe(setEntries)
        return unsubscribe
    }, [cart])

    const handleRemoveEntry = (id: string) => {
        cart.removeEntry(id)
    }

    const handleClearCart = () => {
        cart.clear()
    }

    if (entries.length === 0) {
        return (
            <div className="cart">
                <h2>Cart</h2>
                <p className="cart-empty">No items in cart</p>
            </div>
        )
    }

    return (
        <div className="cart">
            <h2>Cart ({entries.length} items)</h2>

            <div className="cart-entries">
                {entries.map((entry) => (
                    <div key={entry.id} className="cart-entry">
                        <div className="cart-entry-info">
                            <div className="cart-entry-filename">{entry.filename}</div>
                            <div className="cart-entry-material">Material: {entry.material.name.toUpperCase()}</div>
                            <div className="cart-entry-cost">€{entry.cost.toFixed(2)}</div>
                        </div>
                        <button
                            className="cart-entry-remove"
                            onClick={() => handleRemoveEntry(entry.id)}
                            title="Remove from cart"
                        >
                            ×
                        </button>
                    </div>
                ))}
            </div>

            <div className="cart-total">
                <strong>Total: €{cart.getTotalCost().toFixed(2)}</strong>
            </div>

            <button className="cart-clear" onClick={handleClearCart}>
                Clear Cart
            </button>
        </div>
    )
}