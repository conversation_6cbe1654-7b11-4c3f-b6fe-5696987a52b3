import { useState, useEffect } from 'preact/hooks'
import type { FunctionComponent } from "preact"
import type { MATERIALS } from './settings'

export type CartEntry = {
    id: string
    filename: string
    material: typeof MATERIALS[number]
    cost: number
    quantity: number
    file: ArrayBuffer
}

export class Cart {
    private entries: CartEntry[] = []
    private listeners: Array<(entries: CartEntry[]) => void> = []

    addEntry(filename: string, material: typeof MATERIALS[number], cost: number, file: ArrayBuffer, quantity: number = 1): void {
        const entry: CartEntry = {
            id: crypto.randomUUID(),
            filename,
            material,
            cost,
            quantity,
            file
        }
        this.entries.push(entry)
        this.notifyListeners()
    }

    updateQuantity(id: string, quantity: number): void {
        if (quantity <= 0) {
            this.removeEntry(id)
            return
        }

        const entry = this.entries.find(e => e.id === id)
        if (entry) {
            entry.quantity = quantity
            this.notifyListeners()
        }
    }

    removeEntry(id: string): void {
        this.entries = this.entries.filter(entry => entry.id !== id)
        this.notifyListeners()
    }

    getEntries(): CartEntry[] {
        return [...this.entries]
    }

    getTotalCost(): number {
        return this.entries.reduce((total, entry) => total + (entry.cost * entry.quantity), 0)
    }

    getTotalItems(): number {
        return this.entries.reduce((total, entry) => total + entry.quantity, 0)
    }

    clear(): void {
        this.entries = []
        this.notifyListeners()
    }

    subscribe(listener: (entries: CartEntry[]) => void): () => void {
        this.listeners.push(listener)
        return () => {
            this.listeners = this.listeners.filter(l => l !== listener)
        }
    }

    private notifyListeners(): void {
        this.listeners.forEach(listener => listener(this.getEntries()))
    }
}

type CartComponentProps = {
    cart: Cart
}

export const CartComponent: FunctionComponent<CartComponentProps> = ({ cart }) => {
    const [entries, setEntries] = useState<CartEntry[]>(cart.getEntries())

    // Subscribe to cart changes
    useEffect(() => {
        const unsubscribe = cart.subscribe(setEntries)
        return unsubscribe
    }, [cart])

    const handleRemoveEntry = (id: string) => {
        cart.removeEntry(id)
    }

    const handleClearCart = () => {
        cart.clear()
    }

    const handleQuantityChange = (id: string, quantity: number) => {
        cart.updateQuantity(id, quantity)
    }

    if (entries.length === 0) {
        return (
            <div className="cart">
                <h2>🛒 Kitten's Shopping Basket 🐾</h2>
                <p className="cart-empty">🐱 Your basket is empty! Add some purr-fect prints! 🐾</p>
            </div>
        )
    }

    return (
        <div className="cart">
            <h2>🛒 Kitten's Shopping Basket 🐾 ({cart.getTotalItems()} items)</h2>

            <div className="cart-entries">
                {entries.map((entry) => (
                    <div key={entry.id} className="cart-entry">
                        <div className="cart-entry-info">
                            <div className="cart-entry-filename">🎀 {entry.filename}</div>
                            <div className="cart-entry-material">💎 Material: {entry.material.name.toUpperCase()}</div>
                            <div className="cart-entry-price">💰 €{entry.cost.toFixed(2)} each</div>
                            <div className="cart-entry-total">✨ €{(entry.cost * entry.quantity).toFixed(2)} total</div>
                        </div>
                        <div className="cart-entry-controls">
                            <div className="cart-entry-quantity">
                                <label htmlFor={`quantity-${entry.id}`}>🐾 Qty:</label>
                                <input
                                    id={`quantity-${entry.id}`}
                                    type="number"
                                    min="1"
                                    value={entry.quantity}
                                    onChange={(e) => handleQuantityChange(entry.id, parseInt(e.currentTarget.value) || 1)}
                                    className="cart-quantity-input"
                                />
                            </div>
                            <button
                                className="cart-entry-remove"
                                onClick={() => handleRemoveEntry(entry.id)}
                                title="Remove this cute item"
                            >
                                ×
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            <div className="cart-total">
                <strong>💖 Total Cuteness: €{cart.getTotalCost().toFixed(2)} 💖</strong>
            </div>

            <button className="cart-clear" onClick={handleClearCart}>
                🗑️ Empty Basket
            </button>
        </div>
    )
}